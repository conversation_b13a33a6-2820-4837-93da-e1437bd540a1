import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

import { RedisService } from './redis.service';

export const REDIS_CLIENT = 'REDIS_CLIENT';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: REDIS_CLIENT,
      useFactory: (configService: ConfigService) => {
        const redisConfig = {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD'),
          db: configService.get<number>('REDIS_DB', 0),
          maxRetriesPerRequest: configService.get<number>('REDIS_MAX_RETRIES', 3),
          retryDelayOnFailover: configService.get<number>('REDIS_RETRY_DELAY', 100),
          enableReadyCheck: configService.get<boolean>('REDIS_READY_CHECK', true),
          lazyConnect: true,
          keepAlive: 30000,
          family: 4,
          connectTimeout: 10000,
          commandTimeout: 5000,
        };

        const redis = new Redis(redisConfig);

        redis.on('connect', () => {
          console.log('Redis connected successfully');
        });

        redis.on('error', error => {
          console.error('Redis connection error:', error);
        });

        redis.on('close', () => {
          console.log('Redis connection closed');
        });

        return redis;
      },
      inject: [ConfigService],
    },
    RedisService,
  ],
  exports: [REDIS_CLIENT, RedisService],
})
export class RedisModule {}
