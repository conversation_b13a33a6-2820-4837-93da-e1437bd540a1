import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@infrastructure/redis/redis.service';

export interface DesignData {
  id: string;
  name: string;
  type: string;
  category?: string;
  description?: string;
  [key: string]: any;
}

export interface ColorData {
  id: string;
  name: string;
  hex?: string;
  rgb?: string;
  category?: string;
  [key: string]: any;
}

export interface InlayDesignData {
  id: string;
  name: string;
  type: 'gold' | 'silver' | 'clear';
  category?: string;
  [key: string]: any;
}

export interface RingSpecData {
  id: string;
  name: string;
  inside: {
    surfaceLength: number;
    surfaceWidth: number;
    designWidth: number;
    textWidth: number;
    power: number;
  };
  outside: {
    surfaceLength: number;
    surfaceWidth: number;
    designWidth: number;
    textWidth: number;
    power: number;
  };
  [key: string]: any;
}

export interface ConfigData {
  designs: DesignData[];
  colors: ColorData[];
  inlayDesigns: InlayDesignData[];
  inlayGoldDesigns: InlayDesignData[];
  inlaySilverDesigns: InlayDesignData[];
  ringSpecs: RingSpecData[];
  general: Record<string, any>;
  postageTypes: any[];
  lastUpdated: Date;
}

@Injectable()
export class DesignColorCacheService {
  private readonly logger = new Logger(DesignColorCacheService.name);
  private readonly CACHE_KEYS = {
    DESIGNS: 'config:designs',
    COLORS: 'config:colors',
    INLAY_DESIGNS: 'config:inlay_designs',
    INLAY_GOLD_DESIGNS: 'config:inlay_gold_designs',
    INLAY_SILVER_DESIGNS: 'config:inlay_silver_designs',
    RING_SPECS: 'config:ring_specs',
    GENERAL: 'config:general',
    POSTAGE_TYPES: 'config:postage_types',
    FULL_CONFIG: 'config:full',
    LAST_UPDATED: 'config:last_updated',
  };
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours in seconds

  constructor(private readonly redisService: RedisService) {}

  /**
   * Cache all design and color data from spreadsheet
   */
  async cacheConfigData(configData: ConfigData): Promise<void> {
    try {
      this.logger.log('Caching design and color data from spreadsheet');

      // Cache individual components
      await Promise.all([
        this.redisService.set(this.CACHE_KEYS.DESIGNS, configData.designs, this.CACHE_TTL),
        this.redisService.set(this.CACHE_KEYS.COLORS, configData.colors, this.CACHE_TTL),
        this.redisService.set(
          this.CACHE_KEYS.INLAY_DESIGNS,
          configData.inlayDesigns,
          this.CACHE_TTL,
        ),
        this.redisService.set(
          this.CACHE_KEYS.INLAY_GOLD_DESIGNS,
          configData.inlayGoldDesigns,
          this.CACHE_TTL,
        ),
        this.redisService.set(
          this.CACHE_KEYS.INLAY_SILVER_DESIGNS,
          configData.inlaySilverDesigns,
          this.CACHE_TTL,
        ),
        this.redisService.set(this.CACHE_KEYS.RING_SPECS, configData.ringSpecs, this.CACHE_TTL),
        this.redisService.set(this.CACHE_KEYS.GENERAL, configData.general, this.CACHE_TTL),
        this.redisService.set(
          this.CACHE_KEYS.POSTAGE_TYPES,
          configData.postageTypes,
          this.CACHE_TTL,
        ),
        this.redisService.set(this.CACHE_KEYS.FULL_CONFIG, configData, this.CACHE_TTL),
        this.redisService.set(
          this.CACHE_KEYS.LAST_UPDATED,
          new Date().toISOString(),
          this.CACHE_TTL,
        ),
      ]);

      this.logger.log('Successfully cached design and color data');
    } catch (error) {
      this.logger.error('Failed to cache config data:', error);
      throw error;
    }
  }

  /**
   * Get all cached config data
   */
  async getConfigData(): Promise<ConfigData | null> {
    try {
      const configData = await this.redisService.get<ConfigData>(this.CACHE_KEYS.FULL_CONFIG);
      return configData;
    } catch (error) {
      this.logger.error('Failed to get config data from cache:', error);
      return null;
    }
  }

  /**
   * Get cached designs
   */
  async getDesigns(): Promise<DesignData[]> {
    try {
      const designs = await this.redisService.get<DesignData[]>(this.CACHE_KEYS.DESIGNS);
      return designs || [];
    } catch (error) {
      this.logger.error('Failed to get designs from cache:', error);
      return [];
    }
  }

  /**
   * Get cached colors
   */
  async getColors(): Promise<ColorData[]> {
    try {
      const colors = await this.redisService.get<ColorData[]>(this.CACHE_KEYS.COLORS);
      return colors || [];
    } catch (error) {
      this.logger.error('Failed to get colors from cache:', error);
      return [];
    }
  }

  /**
   * Get cached inlay designs
   */
  async getInlayDesigns(): Promise<InlayDesignData[]> {
    try {
      const inlayDesigns = await this.redisService.get<InlayDesignData[]>(
        this.CACHE_KEYS.INLAY_DESIGNS,
      );
      return inlayDesigns || [];
    } catch (error) {
      this.logger.error('Failed to get inlay designs from cache:', error);
      return [];
    }
  }

  /**
   * Get cached gold inlay designs
   */
  async getInlayGoldDesigns(): Promise<InlayDesignData[]> {
    try {
      const goldDesigns = await this.redisService.get<InlayDesignData[]>(
        this.CACHE_KEYS.INLAY_GOLD_DESIGNS,
      );
      return goldDesigns || [];
    } catch (error) {
      this.logger.error('Failed to get gold inlay designs from cache:', error);
      return [];
    }
  }

  /**
   * Get cached silver inlay designs
   */
  async getInlaySilverDesigns(): Promise<InlayDesignData[]> {
    try {
      const silverDesigns = await this.redisService.get<InlayDesignData[]>(
        this.CACHE_KEYS.INLAY_SILVER_DESIGNS,
      );
      return silverDesigns || [];
    } catch (error) {
      this.logger.error('Failed to get silver inlay designs from cache:', error);
      return [];
    }
  }

  /**
   * Get cached ring specifications
   */
  async getRingSpecs(): Promise<RingSpecData[]> {
    try {
      const ringSpecs = await this.redisService.get<RingSpecData[]>(this.CACHE_KEYS.RING_SPECS);
      return ringSpecs || [];
    } catch (error) {
      this.logger.error('Failed to get ring specs from cache:', error);
      return [];
    }
  }

  /**
   * Get cached general config
   */
  async getGeneralConfig(): Promise<Record<string, any>> {
    try {
      const general = await this.redisService.get<Record<string, any>>(this.CACHE_KEYS.GENERAL);
      return general || {};
    } catch (error) {
      this.logger.error('Failed to get general config from cache:', error);
      return {};
    }
  }

  /**
   * Get cached postage types
   */
  async getPostageTypes(): Promise<any[]> {
    try {
      const postageTypes = await this.redisService.get<any[]>(this.CACHE_KEYS.POSTAGE_TYPES);
      return postageTypes || [];
    } catch (error) {
      this.logger.error('Failed to get postage types from cache:', error);
      return [];
    }
  }

  /**
   * Check if config data is cached and not expired
   */
  async isConfigCached(): Promise<boolean> {
    try {
      const exists = await this.redisService.exists(this.CACHE_KEYS.FULL_CONFIG);
      return exists;
    } catch (error) {
      this.logger.error('Failed to check if config is cached:', error);
      return false;
    }
  }

  /**
   * Get last updated timestamp
   */
  async getLastUpdated(): Promise<Date | null> {
    try {
      const lastUpdated = await this.redisService.get<string>(this.CACHE_KEYS.LAST_UPDATED);
      return lastUpdated ? new Date(lastUpdated) : null;
    } catch (error) {
      this.logger.error('Failed to get last updated timestamp:', error);
      return null;
    }
  }

  /**
   * Clear all cached config data
   */
  async clearConfigCache(): Promise<void> {
    try {
      const keys = Object.values(this.CACHE_KEYS);
      await this.redisService.delMany(keys);
      this.logger.log('Cleared all config cache');
    } catch (error) {
      this.logger.error('Failed to clear config cache:', error);
      throw error;
    }
  }

  /**
   * Find design by name or ID
   */
  async findDesign(identifier: string): Promise<DesignData | null> {
    try {
      const designs = await this.getDesigns();
      return (
        designs.find(
          design =>
            design.id === identifier ||
            design.name?.toLowerCase() === identifier.toLowerCase() ||
            design.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find design ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Find color by name or ID
   */
  async findColor(identifier: string): Promise<ColorData | null> {
    try {
      const colors = await this.getColors();
      return (
        colors.find(
          color =>
            color.id === identifier ||
            color.name?.toLowerCase() === identifier.toLowerCase() ||
            color.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find color ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Find inlay design by name or ID
   */
  async findInlayDesign(
    identifier: string,
    type?: 'gold' | 'silver' | 'clear',
  ): Promise<InlayDesignData | null> {
    try {
      let designs: InlayDesignData[] = [];

      if (type === 'gold') {
        designs = await this.getInlayGoldDesigns();
      } else if (type === 'silver') {
        designs = await this.getInlaySilverDesigns();
      } else {
        designs = await this.getInlayDesigns();
      }

      return (
        designs.find(
          design =>
            design.id === identifier ||
            design.name?.toLowerCase() === identifier.toLowerCase() ||
            design.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find inlay design ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Get all available design names for validation
   */
  async getDesignNames(): Promise<string[]> {
    try {
      const designs = await this.getDesigns();
      return designs.map(design => design.name).filter(Boolean);
    } catch (error) {
      this.logger.error('Failed to get design names:', error);
      return [];
    }
  }

  /**
   * Get all available color names for validation
   */
  async getColorNames(): Promise<string[]> {
    try {
      const colors = await this.getColors();
      return colors.map(color => color.name).filter(Boolean);
    } catch (error) {
      this.logger.error('Failed to get color names:', error);
      return [];
    }
  }

  /**
   * Validate if a design exists
   */
  async validateDesign(designName: string): Promise<boolean> {
    try {
      const design = await this.findDesign(designName);
      return design !== null;
    } catch (error) {
      this.logger.error(`Failed to validate design ${designName}:`, error);
      return false;
    }
  }

  /**
   * Validate if a color exists
   */
  async validateColor(colorName: string): Promise<boolean> {
    try {
      const color = await this.findColor(colorName);
      return color !== null;
    } catch (error) {
      this.logger.error(`Failed to validate color ${colorName}:`, error);
      return false;
    }
  }
}
