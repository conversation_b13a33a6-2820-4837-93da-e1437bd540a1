import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OAuthToken } from '../domain/common/entities/oauth-token.entity';
import { OAuthTokenRepository } from '../infrastructure/database/repositories/oauth-token.repository';

import { DateService } from './services/date.service';
import { CryptoService } from './services/crypto.service';
import { ValidationService } from './services/validation.service';
import { TransformService } from './services/transform.service';
import { EncryptionService } from './services/encryption.service';
import { TokenManagerService } from './services/token-manager.service';
import { DesignColorCacheService } from './services/design-color-cache.service';
import { SpreadsheetConfigService } from './services/spreadsheet-config.service';
import { ConfigInitializationService } from './services/config-initialization.service';

@Global()
@Module({
  imports: [ConfigModule, TypeOrmModule.forFeature([OAuthToken])],
  providers: [
    DateService,
    CryptoService,
    ValidationService,
    TransformService,
    EncryptionService,
    OAuthTokenRepository,
    TokenManagerService,
    DesignColorCacheService,
    SpreadsheetConfigService,
    ConfigInitializationService,
  ],
  exports: [
    DateService,
    CryptoService,
    ValidationService,
    TransformService,
    EncryptionService,
    TokenManagerService,
    DesignColorCacheService,
    SpreadsheetConfigService,
    ConfigInitializationService,
  ],
})
export class SharedModule {}
