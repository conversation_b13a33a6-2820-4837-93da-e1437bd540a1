import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EtsyApiService } from '@infrastructure/external/etsy/etsy-api.service';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';

export interface EtsyOrderSyncJobData {
  startDate?: string;
  endDate?: string;
  offset: number;
  limit: number;
  totalExpected?: number;
  jobId: string;
  forceUpdate: boolean;
}

export interface EtsyOrderSyncJobResult {
  ordersProcessed: number;
  ordersCreated: number;
  ordersUpdated: number;
  ordersSkipped: number;
  hasMore: boolean;
  nextOffset?: number;
  errors: string[];
}

@Processor('etsy-order-sync')
@Injectable()
export class EtsyOrderSyncProcessor {
  private readonly logger = new Logger(EtsyOrderSyncProcessor.name);

  constructor(
    private readonly etsyApiService: EtsyApiService,
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
  ) {}

  @Process('sync-orders-batch')
  async handleOrderSyncBatch(job: Job<EtsyOrderSyncJobData>): Promise<EtsyOrderSyncJobResult> {
    const { startDate, endDate, offset, limit, jobId, forceUpdate } = job.data;

    this.logger.log(
      `Processing Etsy order sync batch - Job: ${jobId}, Offset: ${offset}, Limit: ${limit}`,
    );

    let ordersProcessed = 0;
    let ordersCreated = 0;
    let ordersUpdated = 0;
    let ordersSkipped = 0;
    const errors: string[] = [];

    try {
      // Update job progress
      await job.progress(10);

      // Build Etsy API parameters
      const apiParams: any = {
        limit: Math.min(limit, 100), // Etsy max limit
        offset,
        sort_on: 'created',
        sort_order: 'down',
      };

      // Add date filters if provided (Etsy uses Unix timestamps)
      if (startDate) {
        const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
        apiParams.min_created = startTimestamp;
      }
      if (endDate) {
        const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);
        apiParams.max_created = endTimestamp;
      }

      this.logger.log(`Fetching receipts from Etsy API with params:`, apiParams);

      // Update job progress
      await job.progress(30);

      // Fetch receipts from Etsy with rate limiting
      const receiptsResponse = await this.etsyApiService.getShopReceipts(apiParams);
      const receipts = receiptsResponse.receipts;

      this.logger.log(`Retrieved ${receipts.length} receipts from Etsy`);

      // Update job progress
      await job.progress(50);

      // Process each receipt
      for (let i = 0; i < receipts.length; i++) {
        const receipt = receipts[i];

        try {
          ordersProcessed++;

          // Check if order already exists
          const existingOrder = await this.orderRepository.findByExternalId(
            receipt.receipt_id?.toString() || '',
            PlatformSource.ETSY,
          );

          if (existingOrder) {
            if (forceUpdate) {
              // Update existing order
              await this.updateExistingOrder(existingOrder, receipt);
              await this.orderRepository.save(existingOrder);
              ordersUpdated++;
              this.logger.debug(`Updated existing Etsy order: ${receipt.receipt_id}`);
            } else {
              // Skip existing order
              ordersSkipped++;
              this.logger.debug(`Skipped existing Etsy order: ${receipt.receipt_id}`);
            }
          } else {
            // Create new order
            await this.processReceipt(receipt);
            ordersCreated++;
            this.logger.debug(`Created new Etsy order: ${receipt.receipt_id}`);
          }

          // Update progress based on processed items
          const progressPercent = 50 + Math.floor((i / receipts.length) * 40);
          await job.progress(progressPercent);

          // Add small delay to respect rate limits
          if (i < receipts.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          const errorMsg = `Failed to process Etsy receipt ${receipt.receipt_id}: ${error.message}`;
          this.logger.error(errorMsg, error);
          errors.push(errorMsg);
          // Continue processing other receipts
        }
      }

      // Determine if there are more records
      const hasMore = receipts.length === limit;
      const nextOffset = hasMore ? offset + limit : undefined;

      // Update job progress to complete
      await job.progress(100);

      const result: EtsyOrderSyncJobResult = {
        ordersProcessed,
        ordersCreated,
        ordersUpdated,
        ordersSkipped,
        hasMore,
        nextOffset,
        errors,
      };

      this.logger.log(
        `Etsy order sync batch completed - Job: ${jobId}, Processed: ${ordersProcessed}, Created: ${ordersCreated}, Updated: ${ordersUpdated}, Skipped: ${ordersSkipped}, HasMore: ${hasMore}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Etsy order sync batch failed - Job: ${jobId}:`, error);
      errors.push(`Batch processing failed: ${error.message}`);

      throw error; // This will mark the job as failed
    }
  }

  /**
   * Process receipt from Etsy and create order
   */
  private async processReceipt(receipt: any): Promise<void> {
    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        this.logger.warn(`Invalid timestamp received: ${timestamp}, using current date`);
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    // Create new order
    const order = this.orderRepository.create({
      externalOrderId: receipt.receipt_id?.toString() || '',
      orderNumber: receipt.receipt_id?.toString() || '',
      status: this.mapEtsyOrderStatus(receipt.was_paid, receipt.was_shipped),
      source: PlatformSource.ETSY,
      externalCreatedAt: safeTimestampToDate(receipt.creation_timestamp),
      customerEmail: receipt.buyer_email || '',
      customerName: receipt.name || '',
      customerPhone: '',
      shippingAddressLine1: receipt.first_line || '',
      shippingAddressLine2: receipt.second_line || '',
      shippingCity: receipt.city || '',
      shippingState: receipt.state || '',
      shippingPostalCode: receipt.zip || '',
      shippingCountry: receipt.country_iso || '',
      subtotalPrice: safePriceValue(receipt.subtotal),
      shippingPrice: safePriceValue(receipt.total_shipping_cost),
      taxAmount: safePriceValue(receipt.total_tax_cost),
      discountAmount: safePriceValue(receipt.discount_amt),
      totalPrice: safePriceValue(receipt.grandtotal),
      currency: receipt.currency_code || 'USD',
      customerNote: receipt.message_from_buyer || '',
      isProcessed: false,
      rawData: {
        platform: 'etsy',
        receipt: receipt,
        syncedAt: new Date().toISOString(),
        processorVersion: '1.0',
      },
      metadata: {
        etsyData: {
          orderId: receipt.order_id,
          paymentMethod: receipt.payment_method,
          wasPaid: receipt.was_paid,
          needsGiftWrap: receipt.needs_gift_wrap,
          giftMessage: receipt.gift_message,
        },
      },
    });

    const savedOrder = await this.orderRepository.save(order);

    // Create order items from transactions
    for (const transaction of receipt.transactions) {
      await this.createOrderItem(savedOrder.id, transaction);
    }
  }

  /**
   * Update existing order with Etsy data
   */
  private async updateExistingOrder(existingOrder: any, receipt: any): Promise<void> {
    const newStatus = this.mapEtsyOrderStatus(receipt.was_paid, receipt.was_shipped);

    if (existingOrder.status !== newStatus) {
      existingOrder.updateStatus(newStatus);
    }

    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Update metadata
    if (!existingOrder.metadata) {
      existingOrder.metadata = {};
    }
    existingOrder.metadata.etsyData = {
      ...existingOrder.metadata.etsyData,
      lastUpdated: safeTimestampToDate(receipt.update_timestamp),
      wasPaid: receipt.was_paid,
      wasShipped: receipt.was_shipped,
    };
  }

  /**
   * Create order item from Etsy transaction
   */
  private async createOrderItem(orderId: string, transaction: any): Promise<void> {
    const sku =
      transaction.sku || `ETSY-${transaction.listing_id}-${transaction.product_id || 'unknown'}`;

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    const unitPrice = safePriceValue(transaction.price);
    const quantity = Number(transaction.quantity) || 1;

    // Extract product attributes from Etsy data
    const productAttributes = this.extractProductAttributes(transaction);

    const orderItem = this.orderItemRepository.create({
      orderId,
      sku,
      title: transaction.title || '',
      style: productAttributes.style,
      color: productAttributes.color,
      size: productAttributes.size,
      design: productAttributes.design,
      quantity,
      unitPrice,
      totalPrice: unitPrice * quantity,
      currency: transaction.price?.currency_code || 'USD',
      externalProductId: transaction.listing_id?.toString() || '',
      isCustom: productAttributes.isCustom,
      isEngrave: productAttributes.isEngrave,
      customization: productAttributes.customization,
      engraving: productAttributes.engraving,
      rawData: {
        platform: 'etsy',
        transaction: transaction,
        syncedAt: new Date().toISOString(),
      },
      metadata: {
        etsyData: {
          transactionId: transaction.transaction_id,
          listingId: transaction.listing_id,
          productId: transaction.product_id,
          variationData: transaction.variations,
          productData: transaction.product_data,
        },
      },
    });

    await this.orderItemRepository.save(orderItem);
  }

  /**
   * Extract product attributes from Etsy transaction data
   */
  private extractProductAttributes(transaction: any): {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    const result = {
      style: undefined as string | undefined,
      color: undefined as string | undefined,
      size: undefined as number | undefined,
      design: undefined as string | undefined,
      isCustom: false,
      isEngrave: false,
      customization: undefined as string | undefined,
      engraving: undefined as string | undefined,
    };

    // 1. Extract from variations (Etsy's primary variation data)
    if (transaction.variations && Array.isArray(transaction.variations)) {
      for (const variation of transaction.variations) {
        const propertyName = variation.formatted_name?.toLowerCase() || '';
        const value = variation.formatted_value || '';

        if (propertyName.includes('color') || propertyName.includes('colour')) {
          result.color = value;
        } else if (propertyName.includes('size')) {
          // Extract size number from formatted value like "12 (6mm bandwidth)"
          const sizeMatch = value.match(/(\d+)/);
          if (sizeMatch) {
            result.size = parseInt(sizeMatch[1], 10);
          }
        } else if (propertyName.includes('style') || propertyName.includes('type')) {
          result.style = value;
        } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
          result.design = value;
        }
      }
    }

    // 2. Extract from product_data (alternative variation format)
    if (transaction.product_data && Array.isArray(transaction.product_data)) {
      for (const productData of transaction.product_data) {
        const propertyName = productData.property_name?.toLowerCase() || '';
        const values = productData.values || [];
        const value = values[0] || '';

        if (
          propertyName.includes('color') ||
          propertyName.includes('colour') ||
          propertyName === 'primary color'
        ) {
          result.color = result.color || value;
        } else if (propertyName.includes('size') || propertyName === 'custom property') {
          // For custom property, check if it contains size info
          const sizeMatch = value.match(/(\d+)/);
          if (sizeMatch) {
            result.size = result.size || parseInt(sizeMatch[1], 10);
          }
        } else if (propertyName.includes('style') || propertyName.includes('type')) {
          result.style = result.style || value;
        } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
          result.design = result.design || value;
        }
      }
    }

    // 3. Extract from SKU as fallback (format: Style-Width-Color-Size)
    if (transaction.sku && (!result.style || !result.color || !result.size)) {
      const skuParts = transaction.sku.split('-');

      if (skuParts.length >= 3) {
        // ComfortFit-6mm-Silver-12 format
        if (!result.style && skuParts[0]) {
          result.style = skuParts[0];
        }
        if (!result.color && skuParts[2]) {
          result.color = skuParts[2];
        }
        if (!result.size && skuParts[3]) {
          const sizeMatch = skuParts[3].match(/(\d+)/);
          if (sizeMatch) {
            result.size = parseInt(sizeMatch[1], 10);
          }
        }
      }
    }

    // 4. Check for customization and engraving
    const title = transaction.title?.toLowerCase() || '';
    const description = transaction.description?.toLowerCase() || '';

    // Check for engraving keywords
    if (
      title.includes('engrav') ||
      description.includes('engrav') ||
      title.includes('custom') ||
      description.includes('custom')
    ) {
      result.isEngrave = title.includes('engrav') || description.includes('engrav');
      result.isCustom = title.includes('custom') || description.includes('custom');
    }

    // Look for personalization in variations
    if (transaction.variations) {
      for (const variation of transaction.variations) {
        const propertyName = variation.formatted_name?.toLowerCase() || '';
        const value = variation.formatted_value || '';

        if (
          propertyName.includes('personalization') ||
          propertyName.includes('engraving') ||
          propertyName.includes('custom text') ||
          propertyName.includes('message')
        ) {
          if (
            value &&
            value.trim() &&
            value.toLowerCase() !== 'none' &&
            value.toLowerCase() !== 'no'
          ) {
            result.isEngrave = true;
            result.engraving = value;
          }
        } else if (propertyName.includes('customization') || propertyName.includes('custom')) {
          if (
            value &&
            value.trim() &&
            value.toLowerCase() !== 'none' &&
            value.toLowerCase() !== 'no'
          ) {
            result.isCustom = true;
            result.customization = value;
          }
        }
      }
    }

    return result;
  }

  /**
   * Map Etsy order status to internal status
   */
  private mapEtsyOrderStatus(wasPaid: boolean, wasShipped: boolean): OrderStatus {
    if (wasShipped) {
      return OrderStatus.SHIPPED;
    }
    if (wasPaid) {
      return OrderStatus.CONFIRMED;
    }
    return OrderStatus.PENDING;
  }
}
